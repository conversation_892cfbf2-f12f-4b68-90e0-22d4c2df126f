/* Custom CSS for Financial Institution Earnings Presentation */

/* Brand Colors */
:root {
    --primary-blue: #2c3e50;
    --accent-purple: #667eea;
    --success-green: #28a745;
    --warning-orange: #fd7e14;
    --light-gray: #f8f9fa;
    --border-gray: #e9ecef;
}

/* Global Overrides */
.reveal {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.reveal .slides section {
    text-align: left;
    line-height: 1.6;
}

.reveal h1, .reveal h2, .reveal h3, .reveal h4 {
    color: var(--primary-blue);
    text-align: center;
    font-weight: 600;
}

/* Financial Highlight Boxes */
.financial-highlight {
    background: linear-gradient(135deg, var(--accent-purple) 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin: 25px 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.financial-highlight h3 {
    color: white;
    margin-bottom: 20px;
}

/* Metric Cards */
.metric-card {
    background: var(--light-gray);
    border: 2px solid var(--border-gray);
    border-radius: 10px;
    padding: 20px;
    margin: 15px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.metric-value {
    font-size: 2.2em;
    font-weight: bold;
    color: var(--success-green);
    margin: 10px 0;
}

/* Code Blocks */
.reveal pre {
    width: 100%;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.reveal code {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85em;
    line-height: 1.4;
}

/* Mathematical Equations */
.reveal .MathJax {
    font-size: 1.1em !important;
}

/* Tables */
.reveal table {
    margin: 20px auto;
    border-collapse: collapse;
    width: 100%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.reveal table th {
    background: var(--primary-blue);
    color: white;
    padding: 12px;
    font-weight: 600;
}

.reveal table td {
    padding: 10px;
    border-bottom: 1px solid var(--border-gray);
}

.reveal table tr:nth-child(even) {
    background: var(--light-gray);
}

/* Email Footer */
.email-footer {
    position: fixed;
    bottom: 15px;
    right: 25px;
    font-size: 0.85em;
    color: #666;
    background: rgba(255,255,255,0.95);
    padding: 8px 15px;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
}

/* Fragment Animations */
.reveal .fragment.highlight-current-blue {
    opacity: 1;
    visibility: inherit;
}

.reveal .fragment.highlight-current-blue.current-fragment {
    color: var(--accent-purple);
    font-weight: bold;
}

/* Strategic Priority Cards */
.strategy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.strategy-card {
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.strategy-card:hover {
    transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .reveal .slides section {
        font-size: 0.9em;
    }
    
    .metric-card {
        margin: 10px 5px;
        padding: 15px;
    }
    
    .metric-value {
        font-size: 1.8em;
    }
    
    .email-footer {
        font-size: 0.75em;
        padding: 6px 12px;
    }
}

/* Print Styles */
@media print {
    .email-footer {
        display: none;
    }
    
    .reveal .slides section {
        page-break-inside: avoid;
    }
}

/* Speaker Notes Styling */
.speaker-notes {
    font-size: 1.1em;
    line-height: 1.5;
    color: #333;
}

/* Progress Bar Customization */
.reveal .progress {
    color: var(--accent-purple);
}

/* Controls Customization */
.reveal .controls {
    color: var(--primary-blue);
}

.reveal .controls .navigate-left,
.reveal .controls .navigate-right,
.reveal .controls .navigate-up,
.reveal .controls .navigate-down {
    border-color: var(--primary-blue);
}

.reveal .controls .navigate-left.enabled:hover,
.reveal .controls .navigate-right.enabled:hover,
.reveal .controls .navigate-up.enabled:hover,
.reveal .controls .navigate-down.enabled:hover {
    border-color: var(--accent-purple);
}
