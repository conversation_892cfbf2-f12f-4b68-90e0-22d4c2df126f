# Customer Analytics: Response Time Distribution by Support Channel

This repository contains a Seaborn violinplot visualization of customer support response times across different channels.

**Email:** <EMAIL>

## Files
- `chart.py` – Python script that generates the violinplot.
- `chart.png` – Visualization (512x512 pixels).
- `README.md` – Project documentation.

## How to Run
```bash
pip install seaborn matplotlib pandas numpy
python chart.py
