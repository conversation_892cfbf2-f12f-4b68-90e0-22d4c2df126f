# Supply Chain Analytics: Correlation Matrix Visualization

This repository contains the correlation analysis and heatmap for the supply chain metrics dataset.

**Email:** <EMAIL>

## Files
- `correlation.csv`: Pearson correlation matrix of the selected metrics.
- `heatmap.png`: Red–White–Green heatmap (512×512 px) representing the correlation matrix.

## Recreate
1. Import the dataset into Excel.
2. Use **Data → Data Analysis → Correlation** (enable Analysis ToolPak if needed).
3. Select the 5 columns, check **Labels in first row**, and output to a new worksheet.
4. Copy the matrix to a new sheet and apply **Home → Conditional Formatting → Color Scales → Red-White-Green**.
5. Take a square screenshot (400×400 to 512×512 px).

## Raw README URL Pattern
Replace placeholders with your details:
```
https://raw.githubusercontent.com/[username]/[repo]/[branch]/[folder]/README.md
```
