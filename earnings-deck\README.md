# Q4 2024 Earnings Report - RevealJS Presentation

## Overview
This is an interactive RevealJS presentation for the quarterly earnings report of a major financial institution. The presentation demonstrates modern web-based presentation capabilities with all requested features implemented.

## Features Implemented ✅

### 1. **Email Integration**
- Contact email `<EMAIL>` is prominently displayed
- Appears on title slide and persistent footer
- Contact information in Q&A section

### 2. **Markdown Content**
- Multiple slides written in Markdown format
- Performance analytics table in Markdown
- Strategic initiatives using Markdown lists

### 3. **Animated Elements (Fragments)**
- Progressive disclosure of financial metrics
- Fade-in, fade-up, zoom-in animations
- Highlight effects for key points
- Sequential reveal of content

### 4. **Code Samples with Syntax Highlighting**
- **Python**: Portfolio risk calculation implementation
- **SQL**: Customer segmentation and profitability analysis
- Syntax highlighting using Highlight.js
- Professional code formatting

### 5. **Mathematical Equations**
- Value at Risk (VaR) formulas
- Return on Equity (ROE) calculations
- Capital allocation optimization
- LaTeX/MathJax rendering

### 6. **Speaker Notes**
- Comprehensive notes for each slide
- Presentation guidance and talking points
- Context and background information
- Press 'S' to open speaker notes view

## File Structure
```
earnings-deck/
├── index.html          # Main presentation file
├── README.md          # This documentation
├── marimo_notebook.ipynb
├── q-excel-correlation-heatmap.xlsx
└── tds4.py
```

## How to Use

### 1. **Open the Presentation**
- Open `index.html` in any modern web browser
- No local server required - uses CDN resources
- Works offline after initial load

### 2. **Navigation Controls**
- **Arrow Keys**: Navigate between slides
- **Space Bar**: Next slide
- **ESC**: Slide overview mode
- **S**: Open speaker notes
- **F**: Fullscreen mode

### 3. **Presentation Features**
- **Fragments**: Content appears progressively
- **Speaker Notes**: Detailed guidance for presenter
- **Math Rendering**: Live LaTeX equation display
- **Code Highlighting**: Syntax-highlighted code blocks

## Technical Stack

- **RevealJS 4.3.1**: Modern presentation framework
- **MathJax**: Mathematical equation rendering
- **Highlight.js**: Code syntax highlighting
- **Custom CSS**: Financial institution styling
- **CDN Resources**: No local dependencies

## Presentation Content

### Slides Overview:
1. **Title Slide** - Introduction with contact information
2. **Executive Summary** - Key highlights with animations
3. **Financial Metrics** - Animated metric cards
4. **Risk Analysis** - Mathematical formulas and VaR calculations
5. **Code Implementation** - Python risk calculation example
6. **Performance Analytics** - Markdown table with KPIs
7. **SQL Analytics** - Customer segmentation query
8. **Market Outlook** - Strategic priorities with animations
9. **Financial Projections** - 2025 targets and formulas
10. **Q&A Section** - Contact information and discussion
11. **Appendix** - Technical implementation details

## Customization

### Styling
- Modify CSS in `<style>` section for branding
- Color scheme uses professional financial theme
- Responsive design for different screen sizes

### Content
- Edit slide content directly in HTML
- Add new slides by copying section structure
- Modify animations by changing fragment classes

## Browser Compatibility
- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers supported

## Contact
For questions or modifications, contact: **<EMAIL>**