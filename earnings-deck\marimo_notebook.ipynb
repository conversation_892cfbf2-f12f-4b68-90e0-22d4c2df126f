{"cells": [{"cell_type": "code", "metadata": {}, "source": ["# <EMAIL>\n", "# Cell 1: Setup and data generation\n", "import marimo as mo\n", "import numpy as np\n", "import pandas as pd\n", "\n", "np.random.seed(42)\n", "n = 200\n", "x = np.linspace(0, 10, n)\n", "y = 3 * x + np.random.normal(scale=2.0, size=n)\n", "df = pd.DataFrame({\"x\": x, \"y\": y})\n", "\n", "# Helper: compute mean y for a given x threshold\n", "def mean_y(threshold):\n", "    return df[df[\"x\"] <= threshold][\"y\"].mean()\n", "\n", "# Data flow: df -> mean_y -> used in downstream cells\n"]}, {"cell_type": "code", "metadata": {}, "source": ["# Cell 2: Interactive slider widget\n", "# Depends on df and mean_y from Cell 1 indirectly through next cell.\n", "slider = mo.ui.slider(0.0, 10.0, value=5.0, step=0.5, label=\"X threshold\")\n", "slider"]}, {"cell_type": "code", "metadata": {}, "source": ["# Cell 3: Dynamic markdown output\n", "# Reads slider.value and mean_y() from Cell 1.\n", "mean_val = mean_y(slider.value)\n", "mo.md(f\"**Mean y** for x ≤ {slider.value}: `{mean_val:.2f}`\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Notes on interactivity\n", "- Re-run Cell 1 to change the dataset; Cells 2 and 3 will reflect changes.\n", "- The slider controls the threshold for x; the markdown updates automatically.\n", "- This is a native Marimo slider (`mo.ui.slider`).\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10"}}, "nbformat": 4, "nbformat_minor": 5}